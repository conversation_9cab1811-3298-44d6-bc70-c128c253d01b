nacos:
  server-addr: ${config_nacos_serveraddr:localhost}
  port: ${config_nacos_port:8848}
  namespace: ${config_nacos_namespace:d55614cb-2190-4854-b324-c2b636ddc3e8}

# 公共配置
spring:
  config:
    activate:
      on-profile: ${config_profile:test}
    import:
      - optional:nacos:${spring.application.name}-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
  servlet:
    multipart:
      enabled: true
      max-file-size: 50MB
      max-request-size: 50MB
  mvc: # swagger启动报错配置
    pathmatch:
      matching-strategy: ant_path_matcher
  application:
    name: dhr-gateway-service
  cloud:
    nacos:
      discovery:
        server-addr: ${nacos.server-addr}:${nacos.port}
        namespace: ${nacos.namespace}
        group: ${spring.profiles.active}
      config:
        file-extension: yaml
        server-addr: ${nacos.server-addr}:${nacos.port}
        group: ${spring.profiles.active}
        enabled: ${config.enable:true}
        namespace: ${nacos.namespace}

# 管理端点配置
management:
  health:
    redis:
      enabled: false  # 禁用Redis健康检查，避免启动时连接失败

server:
  port:  ${config_server_port:9110}

hystrix:
  threadpool:
    default:
      coreSize: 50
      maximumSize: 10000
      allowMaximumSizeToDivergeFromCoreSize: true
      maxQueueSize: -1
  command:
    default:
      execution:
        timeout:
          enabled: false
        isolation:
          thread:
            timeoutInMilliseconds: 3600000

ribbon:
  ConnectTimeout: 900000
  ReadTimeout: 900000

logging:
  file:
    name: logs/${spring.application.name}/info/log_info.log
  level:
    root: info  #输出日志级别
