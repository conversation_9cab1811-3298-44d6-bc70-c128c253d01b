nacos:
  server-addr: ${config_nacos_serveraddr:localhost}
  port: ${config_nacos_port:8848}
  namespace: ${config_nacos_namespace:d55614cb-2190-4854-b324-c2b636ddc3e8}

spring:
  config:
    activate:
      on-profile: ${config_profile:test}
    import:
      - optional:nacos:${spring.application.name}-${config_profile:test}.yaml?server-addr=localhost:8848
  mvc:
    pathmatch:
      matching-strategy: path_pattern_parser
  application:
    name: dhr-ssc-service
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  cloud:
    nacos:
      discovery:
        server-addr: ${nacos.server-addr}:${nacos.port}
        namespace: ""
        group: ${config_profile:test}
      config:
        file-extension: yaml
        server-addr: ${nacos.server-addr}:${nacos.port}
        group: ${config_profile:test}
        enabled: ${config.enable:true}
        namespace: ${nacos.namespace}
        import-check:
          enabled: false

  #i18n
  messages:
    basename: i18n.message
    encoding: UTF-8

server:
  port: ${config_server_port:9027}

dhr:
  swagger:
    enabled: true
    docket:
      basic:
        title: 员工自助首页API
        base-package: com.deloitte.dhr.ssc #需要扫描的包名

  task:
    core-pool-size: 10 #核心线程
    queue-capacity: 5  #任务队列长度
    max-pool-size: 20  #最大线程树
    thread-name-prefix: dhr_basic_ssc  #任务线程前置名

logging:
  file:
    name: logs/${spring.application.name}/info/log_info.log
  level:
    root: info  #输出日志级别

# 禁用 Feign Hystrix 支持
feign:
  hystrix:
    enabled: false
  circuitbreaker:
    enabled: false

# 禁用 OpenTelemetry 自动配置
otel:
  sdk:
    disabled: true
  metrics:
    exporter: none
  traces:
    exporter: none


