nacos:
  server-addr: ${config_nacos_serveraddr:localhost}
  port: ${config_nacos_port:8848}
  namespace: ${config_nacos_namespace:d55614cb-2190-4854-b324-c2b636ddc3e8}


spring:
  config:
    activate:
      on-profile: ${config_profile:test}
  mvc: # swagger启动报错配置
    pathmatch:
      matching-strategy: ant_path_matcher
  main:
    allow-bean-definition-overriding: true
  application:
    name: dhr-utility-service
  cloud:
    nacos:
      discovery:
        server-addr: ${nacos.server-addr}:${nacos.port}
        namespace: ${nacos.namespace}
        group: ${spring.profiles.active}
      config:
        file-extension: yaml
        server-addr: ${nacos.server-addr}:${nacos.port}
        group: ${spring.profiles.active}
        enabled: ${config.enable:true}
        namespace: ${nacos.namespace}
  config:
    import:
      - optional:nacos:${spring.application.name}-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}

server:
  #端口号
  port: ${config_server_port:9025}

logging:
  file:
    name: logs/${spring.application.name}/info/log_info.log
  level:
    root: info  #输出日志级别
#  log:
#    appender:
#      elk:
#        enable: true #开启ELK日志
#        ip: ************ #logstash 地址
#        port: 5045       #logstash 地址

feign:
  hystrix:
    enabled: false
  circuitbreaker:
    enabled: false
  compression:
    request:
      enabled: true
      mime-types[0]: text/xml
      mime-types[1]: application/xml
      mime-types[2]: application/json
    response:
      enabled: true
  client:
    config:
      default:
        connectTimeout: 360000
        readTimeout: 360000
ribbon:
  eager-load:
    enabled: true

