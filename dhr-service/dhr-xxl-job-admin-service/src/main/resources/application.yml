nacos:
  server-addr: ${config_nacos_serveraddr:localhost}
  port: ${config_nacos_port:8848}
  namespace: ${config_nacos_namespace:d55614cb-2190-4854-b324-c2b636ddc3e8}

spring:
  config:
    activate:
      on-profile: ${config_profile:test}
  mvc: # swagger启动报错配置
    pathmatch:
      matching-strategy: ant_path_matcher
  application:
    name: dhr-xxl-job-admin-service

  main:
    allow-bean-definition-overriding: true
  cloud:
    nacos:
      discovery:
        server-addr: ${nacos.server-addr}:${nacos.port}
        namespace: ${nacos.namespace}
        group: ${spring.profiles.active}
      config:
        file-extension: yaml
        server-addr: ${nacos.server-addr}:${nacos.port}
        group: ${spring.profiles.active}
        enabled: ${config.enable:true}
        namespace: ${nacos.namespace}
  config:
    import:
      - optional:nacos:${spring.application.name}-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}

server:
  port: ${config_server_port:9040}


logging:
  file:
    name: logs/${spring.application.name}/info/log_info.log
  level:
    root: info  #输出日志级别

